import { prisma } from '../../lib/prisma.js';

interface WebhookSyncData {
  domainId: string;
  catchAllAliasId: string;
  webhookId: string;
  syncEnabled?: boolean;
}

interface SyncResult {
  success: boolean;
  syncId?: string;
  error?: string;
}

export class WebhookSyncService {
  
  /**
   * Create a new webhook synchronization relationship
   */
  async createSync(data: WebhookSyncData): Promise<SyncResult> {
    try {
      // Check if sync relationship already exists
      const existingSync = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: data.domainId,
            catchAllAliasId: data.catchAllAliasId
          }
        }
      });

      if (existingSync) {
        // Update existing sync relationship
        const updatedSync = await prisma.webhookSync.update({
          where: { id: existingSync.id },
          data: {
            webhookId: data.webhookId,
            syncEnabled: data.syncEnabled ?? true
          }
        });

        return {
          success: true,
          syncId: updatedSync.id
        };
      }

      // Create new sync relationship
      const newSync = await prisma.webhookSync.create({
        data: {
          domainId: data.domainId,
          catchAllAliasId: data.catchAllAliasId,
          webhookId: data.webhookId,
          syncEnabled: data.syncEnabled ?? true
        }
      });

      return {
        success: true,
        syncId: newSync.id
      };

    } catch (error) {
      console.error('Error creating webhook sync:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update domain webhook when catch-all alias webhook changes
   */
  async syncAliasWebhookToDomain(aliasId: string, newWebhookId: string): Promise<SyncResult> {
    try {
      // Find active sync relationships for this alias
      const syncRelationships = await prisma.webhookSync.findMany({
        where: {
          catchAllAliasId: aliasId,
          syncEnabled: true
        },
        include: {
          domain: true,
          alias: true
        }
      });

      // If no sync relationships exist, check if this is a catch-all alias and auto-establish sync
      if (syncRelationships.length === 0) {
        const alias = await prisma.alias.findUnique({
          where: { id: aliasId },
          include: { domain: true }
        });

        if (!alias || !alias.email.startsWith('*@')) {
          return { success: true }; // Not a catch-all alias, nothing to sync
        }

        // Auto-establish sync relationship for catch-all alias
        await prisma.webhookSync.create({
          data: {
            domainId: alias.domainId,
            catchAllAliasId: aliasId,
            webhookId: newWebhookId,
            syncEnabled: true
          }
        });

        // Update domain webhook
        await prisma.domain.update({
          where: { id: alias.domainId },
          data: { webhookId: newWebhookId }
        });

        console.log(`Auto-established sync relationship and synced webhook ${newWebhookId} to domain ${alias.domain.domain}`);
        return { success: true };
      }

      // Update domain webhooks for existing sync relationships
      const updatePromises = syncRelationships.map(async (sync) => {
        // Update domain webhook
        await prisma.domain.update({
          where: { id: sync.domainId },
          data: { webhookId: newWebhookId }
        });

        // Update sync relationship
        await prisma.webhookSync.update({
          where: { id: sync.id },
          data: { webhookId: newWebhookId }
        });

        return sync.domainId;
      });

      const updatedDomainIds = await Promise.all(updatePromises);

      console.log(`Synced webhook ${newWebhookId} to domains: ${updatedDomainIds.join(', ')}`);

      return { success: true };

    } catch (error) {
      console.error('Error syncing alias webhook to domain:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update catch-all alias webhook when domain webhook changes
   */
  async syncDomainWebhookToAlias(domainId: string, newWebhookId: string): Promise<SyncResult> {
    try {
      // Find active sync relationships for this domain
      const syncRelationships = await prisma.webhookSync.findMany({
        where: {
          domainId: domainId,
          syncEnabled: true
        },
        include: {
          domain: true,
          alias: true
        }
      });

      // If no sync relationships exist, check for catch-all aliases and auto-establish sync
      if (syncRelationships.length === 0) {
        const catchAllAliases = await prisma.alias.findMany({
          where: {
            domainId: domainId,
            email: {
              startsWith: '*@'
            }
          }
        });

        if (catchAllAliases.length === 0) {
          return { success: true }; // No catch-all aliases, nothing to sync
        }

        // Auto-establish sync relationships for catch-all aliases
        const autoSyncPromises = catchAllAliases.map(async (alias) => {
          // Create sync relationship
          await prisma.webhookSync.create({
            data: {
              domainId: domainId,
              catchAllAliasId: alias.id,
              webhookId: newWebhookId,
              syncEnabled: true
            }
          });

          // Update alias webhook
          await prisma.alias.update({
            where: { id: alias.id },
            data: { webhookId: newWebhookId }
          });

          console.log(`Auto-established sync relationship for catch-all alias ${alias.email}`);
          return alias.id;
        });

        const updatedAliasIds = await Promise.all(autoSyncPromises);
        console.log(`Auto-synced webhook ${newWebhookId} to catch-all aliases: ${updatedAliasIds.join(', ')}`);

        return { success: true };
      }

      // Update alias webhooks for existing sync relationships
      const updatePromises = syncRelationships.map(async (sync) => {
        // Update alias webhook
        await prisma.alias.update({
          where: { id: sync.catchAllAliasId },
          data: { webhookId: newWebhookId }
        });

        // Update sync relationship
        await prisma.webhookSync.update({
          where: { id: sync.id },
          data: { webhookId: newWebhookId }
        });

        return sync.catchAllAliasId;
      });

      const updatedAliasIds = await Promise.all(updatePromises);

      console.log(`Synced webhook ${newWebhookId} to aliases: ${updatedAliasIds.join(', ')}`);

      return { success: true };

    } catch (error) {
      console.error('Error syncing domain webhook to alias:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Remove webhook synchronization relationship
   */
  async removeSync(domainId: string, catchAllAliasId: string): Promise<SyncResult> {
    try {
      const deletedSync = await prisma.webhookSync.deleteMany({
        where: {
          domainId: domainId,
          catchAllAliasId: catchAllAliasId
        }
      });

      return {
        success: true,
        syncId: `${deletedSync.count} sync(s) removed`
      };

    } catch (error) {
      console.error('Error removing webhook sync:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Enable or disable webhook synchronization
   */
  async toggleSync(domainId: string, catchAllAliasId: string, enabled: boolean): Promise<SyncResult> {
    try {
      const updatedSync = await prisma.webhookSync.updateMany({
        where: {
          domainId: domainId,
          catchAllAliasId: catchAllAliasId
        },
        data: {
          syncEnabled: enabled
        }
      });

      return {
        success: true,
        syncId: `${updatedSync.count} sync(s) ${enabled ? 'enabled' : 'disabled'}`
      };

    } catch (error) {
      console.error('Error toggling webhook sync:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get all sync relationships for a domain
   */
  async getDomainSyncs(domainId: string) {
    try {
      return await prisma.webhookSync.findMany({
        where: { domainId },
        include: {
          domain: true,
          alias: true,
          webhook: true
        }
      });
    } catch (error) {
      console.error('Error getting domain syncs:', error);
      return [];
    }
  }

  /**
   * Get all sync relationships for an alias
   */
  async getAliasSyncs(aliasId: string) {
    try {
      return await prisma.webhookSync.findMany({
        where: { catchAllAliasId: aliasId },
        include: {
          domain: true,
          alias: true,
          webhook: true
        }
      });
    } catch (error) {
      console.error('Error getting alias syncs:', error);
      return [];
    }
  }

  /**
   * Establish sync relationships for all existing catch-all aliases that don't have them
   */
  async establishMissingSyncRelationships(): Promise<SyncResult> {
    try {
      // Find all catch-all aliases without sync relationships
      const catchAllAliases = await prisma.alias.findMany({
        where: {
          email: {
            startsWith: '*@'
          }
        },
        include: {
          domain: true,
          webhookSyncs: true
        }
      });

      const aliasesWithoutSync = catchAllAliases.filter(alias => alias.webhookSyncs.length === 0);

      if (aliasesWithoutSync.length === 0) {
        return {
          success: true,
          syncId: 'No missing sync relationships found'
        };
      }

      // Create sync relationships for aliases without them
      const createPromises = aliasesWithoutSync.map(async (alias) => {
        await prisma.webhookSync.create({
          data: {
            domainId: alias.domainId,
            catchAllAliasId: alias.id,
            webhookId: alias.webhookId, // Use the alias's current webhook
            syncEnabled: true
          }
        });

        // Ensure domain and alias have the same webhook
        if (alias.domain.webhookId !== alias.webhookId) {
          await prisma.domain.update({
            where: { id: alias.domainId },
            data: { webhookId: alias.webhookId }
          });
        }

        return alias.email;
      });

      const establishedAliases = await Promise.all(createPromises);

      console.log(`Established sync relationships for ${establishedAliases.length} catch-all aliases: ${establishedAliases.join(', ')}`);

      return {
        success: true,
        syncId: `${establishedAliases.length} sync relationships established`
      };

    } catch (error) {
      console.error('Error establishing missing sync relationships:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Cleanup orphaned sync relationships
   */
  async cleanupOrphanedSyncs(): Promise<SyncResult> {
    try {
      // Remove syncs where domain, alias, or webhook no longer exists
      const orphanedSyncs = await prisma.webhookSync.findMany({
        include: {
          domain: true,
          alias: true,
          webhook: true
        }
      });

      const orphanedIds = orphanedSyncs
        .filter(sync => !sync.domain || !sync.alias || !sync.webhook)
        .map(sync => sync.id);

      if (orphanedIds.length > 0) {
        await prisma.webhookSync.deleteMany({
          where: {
            id: { in: orphanedIds }
          }
        });

        console.log(`Cleaned up ${orphanedIds.length} orphaned webhook sync relationships`);
      }

      return {
        success: true,
        syncId: `${orphanedIds.length} orphaned syncs cleaned up`
      };

    } catch (error) {
      console.error('Error cleaning up orphaned syncs:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
