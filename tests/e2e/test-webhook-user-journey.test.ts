import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import {
  setupTestDatabase,
  prisma,
  createTestFastifyApp
} from '../setup/test-db-setup.js';

describe('Test Webhook User Journey (E2E)', () => {
  setupTestDatabase();

  let app: FastifyInstance;
  let authToken: string;
  let userId: string;
  let testEmail: string;

  beforeAll(async () => {
    app = await createTestFastifyApp();

    // Register necessary routes
    await app.register(async function (fastify) {
      await fastify.register((await import('../../src/backend/routes/auth.js')).default, { prefix: '/api/auth' });
      await fastify.register((await import('../../src/backend/routes/email.js')).emailRoutes, { prefix: '/api/email' });
      await fastify.register((await import('../../src/backend/routes/logs.routes.js')).logsRoutes, { prefix: '/api' });
    });

    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.email.deleteMany({
      where: { isTestWebhook: true }
    });
  });

  test('complete user journey: signup → get test email → send email → view logs', async () => {
    // Step 1: User signs up
    const signupResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/register',
      payload: {
        email: '<EMAIL>',
        password: 'securepassword123',
        name: 'Test User'
      }
    });

    expect(signupResponse.statusCode).toBe(201);
    const signupBody = JSON.parse(signupResponse.body);

    expect(signupBody.success).toBe(true);
    expect(signupBody.user).toBeDefined();
    
    userId = signupBody.user.id;
    authToken = signupBody.token;

    // Step 2: User gets their test email address
    const userIdSuffix = userId.slice(-8);
    testEmail = `${userIdSuffix}@test.emailconnect.eu`;
    


    // Step 3: User sends an email to their test address
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: My First Test Email
Date: ${new Date().toUTCString()}
Message-ID: <first-test-${Date.now()}@gmail.com>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

Hello!

I'm testing the webhook functionality. This is my first email to see how it works.

The email should appear in my logs with the full JSON payload.

Best regards,
Test User`;

    const emailResponse = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain',
        'X-Email-Source': 'test-journey'
      }
    });

    expect(emailResponse.statusCode).toBe(202);
    const emailBody = JSON.parse(emailResponse.body);
    expect(emailBody.success).toBe(true);
    expect(emailBody.status).toBe('delivered');

    // Step 4: User checks their logs
    const logsResponse = await app.inject({
      method: 'GET',
      url: '/api/logs',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(logsResponse.statusCode).toBe(200);
    const logsBody = JSON.parse(logsResponse.body);
    expect(logsBody.logs).toBeDefined();
    expect(logsBody.logs.length).toBeGreaterThan(0);

    // Find our test email in the logs
    const testEmailLog = logsBody.logs.find((log: any) => 
      log.messageId === emailBody.messageId
    );

    expect(testEmailLog).toBeDefined();
    expect(testEmailLog.fromAddress).toBe('<EMAIL>');
    expect(testEmailLog.subject).toBe('My First Test Email');
    expect(testEmailLog.deliveryStatus).toBe('DELIVERED');

    // Step 5: User views the webhook payload (simulating frontend click)
    const emailRecord = await prisma.email.findUnique({
      where: { messageId: emailBody.messageId }
    });

    expect(emailRecord).toBeTruthy();
    expect(emailRecord!.isTestWebhook).toBe(true);
    // expect(emailRecord!.webhookPayload).toBeTruthy(); // TODO: Update for new architecture

    const payload = emailRecord!.webhookPayload as any;
    
    // Verify the payload structure that user would see
    expect(payload.message.sender.email).toBe('<EMAIL>');
    expect(payload.message.recipient.email).toBe(testEmail);
    expect(payload.message.subject).toBe('My First Test Email');
    expect(payload.message.content.text).toContain('testing the webhook functionality');
    expect(payload.envelope.messageId).toBe(emailBody.messageId);
    expect(payload.envelope.processed.domain).toBe('test.emailconnect.eu');

    // Step 6: Verify user's usage was NOT tracked (test webhooks don't count)
    const updatedUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    expect(updatedUser!.currentMonthEmails).toBe(0); // Should remain 0 for test webhooks
    expect(updatedUser!.monthlyEmailLimit).toBe(50); // Default limit

    console.log('✅ Complete user journey test passed!');
    console.log(`📧 Test email: ${testEmail}`);
    console.log(`📊 Usage: ${updatedUser!.currentMonthEmails}/${updatedUser!.monthlyEmailLimit}`);
    console.log(`🔍 Message ID: ${emailBody.messageId}`);
  });

  test('user can send multiple test emails and see them all', async () => {
    // Create user
    const signupResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/register',
      payload: {
        email: '<EMAIL>',
        password: 'password123'
      }
    });

    const { user, token } = JSON.parse(signupResponse.body);
    const testEmail = `${user.id.slice(-8)}@test.emailconnect.eu`;

    // Send multiple emails
    const emailSubjects = [
      'First Test Email',
      'Second Test Email', 
      'Third Test Email'
    ];

    const messageIds: string[] = [];

    for (let i = 0; i < emailSubjects.length; i++) {
      const rawEmail = `From: sender${i}@example.com
To: ${testEmail}
Subject: ${emailSubjects[i]}
Date: ${new Date().toUTCString()}
Message-ID: <multi-test-${i}-${Date.now()}@example.com>

This is test email number ${i + 1}.`;

      const response = await app.inject({
        method: 'POST',
        url: '/api/email/process',
        payload: rawEmail,
        headers: { 'Content-Type': 'text/plain' }
      });

      expect(response.statusCode).toBe(202);
      const body = JSON.parse(response.body);
      messageIds.push(body.messageId);

      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // Check logs show all emails
    const logsResponse = await app.inject({
      method: 'GET',
      url: '/api/logs',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const logsBody = JSON.parse(logsResponse.body);
    
    // Should have all 3 emails
    expect(logsBody.logs.length).toBe(3);
    
    // Verify all message IDs are present
    const logMessageIds = logsBody.logs.map((log: any) => log.messageId);
    for (const messageId of messageIds) {
      expect(logMessageIds).toContain(messageId);
    }

    // Verify they're ordered by creation time (newest first)
    const timestamps = logsBody.logs.map((log: any) => new Date(log.createdAt).getTime());
    for (let i = 1; i < timestamps.length; i++) {
      expect(timestamps[i-1]).toBeGreaterThanOrEqual(timestamps[i]);
    }

    // Verify usage tracking (test webhooks don't count toward usage)
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentMonthEmails: true }
    });

    expect(updatedUser!.currentMonthEmails).toBe(0); // Should remain 0 for test webhooks
  });

  test('user hits email limit and gets proper error', async () => {
    // Create user with low limit
    const signupResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/register',
      payload: {
        email: '<EMAIL>',
        password: 'password123'
      }
    });

    const { user } = JSON.parse(signupResponse.body);
    
    // Set user to be at limit
    await prisma.user.update({
      where: { id: user.id },
      data: { 
        currentMonthEmails: 50, // At the default limit
        monthlyEmailLimit: 50
      }
    });

    const testEmail = `${user.id.slice(-8)}@test.emailconnect.eu`;

    // Try to send email when at limit
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Should Be Rejected
Date: ${new Date().toUTCString()}
Message-ID: <limit-test-${Date.now()}@example.com>

This should be rejected.`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: { 'Content-Type': 'text/plain' }
    });

    expect(response.statusCode).toBe(429);
    const body = JSON.parse(response.body);
    expect(body.error).toBe('Too Many Requests');
    expect(body.message).toContain('Monthly email limit exceeded');
    expect(body.currentUsage).toBe(50);
    expect(body.monthlyLimit).toBe(50);
  });
});
