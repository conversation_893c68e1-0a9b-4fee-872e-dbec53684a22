import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestWebhook,
  createTestDomain,
  createTestDomainWithCatchAll} from '../../setup/test-db-setup';

describe('User Authentication & Management', () => {
  setupTestDatabase();

  test('should prevent creating user with same email', async () => {
    // Create first user
    await createTestUser({ email: '<EMAIL>' });
    
    // Attempt to create user with same email should fail
    await expect(
      createTestUser({ email: '<EMAIL>' })
    ).rejects.toThrow();
  });

  test('should allow users to have multiple webhooks', async () => {
    const user = await createTestUser();

    const webhook1 = await createTestWebhook(user.id, {
      name: 'Production Webhook',
      url: 'https://prod.example.com/webhook',
    });

    const webhook2 = await createTestWebhook(user.id, {
      name: 'Staging Webhook',
      url: 'https://staging.example.com/webhook',
    });

    const webhook3 = await createTestWebhook(user.id, {
      name: 'Development Webhook',
      url: 'https://dev.example.com/webhook',
    });

    const userWebhooks = await prisma.webhook.findMany({
      where: { userId: user.id },
    });

    expect(userWebhooks).toHaveLength(3);
    expect(userWebhooks.map(w => w.name)).toContain('Production Webhook');
    expect(userWebhooks.map(w => w.name)).toContain('Staging Webhook');
    expect(userWebhooks.map(w => w.name)).toContain('Development Webhook');
  });

  test('should allow users to have multiple domains', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);

    const domain1 = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: 'first-domain.com',
    });

    const domain2 = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: 'second-domain.com',
    });

    const domain3 = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: 'third-domain.com', 
    });

    const userDomains = await prisma.domain.findMany({
      where: { userId: user.id },
    });

    expect(userDomains).toHaveLength(3);
    expect(userDomains.map(d => d.domain)).toContain('first-domain.com');
    expect(userDomains.map(d => d.domain)).toContain('second-domain.com');
    expect(userDomains.map(d => d.domain)).toContain('third-domain.com');
  });

  test('should handle user verification status', async () => {
    const verifiedUser = await createTestUser({
      email: '<EMAIL>',
      verified: true,
    });

    const unverifiedUser = await createTestUser({
      email: '<EMAIL>', 
      verified: false,
    });

    expect(verifiedUser.verified).toBe(true);
    expect(unverifiedUser.verified).toBe(false);

    // Update verification status
    const updatedUser = await prisma.user.update({
      where: { id: unverifiedUser.id },
      data: { verified: true },
    });

    expect(updatedUser.verified).toBe(true);
  });

  test('should track user usage statistics', async () => {
    const user = await createTestUser({
      monthlyEmailLimit: 100,
      currentMonthEmails: 25,
      planType: 'pro',
    });

    expect(user.monthlyEmailLimit).toBe(100);
    expect(user.currentMonthEmails).toBe(25);
    expect(user.planType).toBe('pro');

    // Simulate email processing increment
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        currentMonthEmails: { increment: 1 }
      },
    });

    expect(updatedUser.currentMonthEmails).toBe(26);
  });
});

describe('User Isolation & Security', () => {
  setupTestDatabase();

  test('should prevent users from accessing other users data', async () => {
    // Create two separate users with their data
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    const user1Webhook = await createTestWebhook(user1.id, { name: 'User 1 Webhook' });
    const user2Webhook = await createTestWebhook(user2.id, { name: 'User 2 Webhook' });

    const user1Domain = await createTestDomainWithCatchAll(user1.id, user1Webhook.id, {
      domain: 'user1.example.com',
    });
    const user2Domain = await createTestDomainWithCatchAll(user2.id, user2Webhook.id, {
      domain: 'user2.example.com',
    });

    // User 1 should only see their own data
    const user1Webhooks = await prisma.webhook.findMany({
      where: { userId: user1.id },
    });
    const user1Domains = await prisma.domain.findMany({
      where: { userId: user1.id },
    });

    expect(user1Webhooks).toHaveLength(1);
    expect(user1Webhooks[0].name).toBe('User 1 Webhook');
    expect(user1Domains).toHaveLength(1);
    expect(user1Domains[0].domain).toBe('user1.example.com');

    // User 2 should only see their own data
    const user2Webhooks = await prisma.webhook.findMany({
      where: { userId: user2.id },
    });
    const user2Domains = await prisma.domain.findMany({
      where: { userId: user2.id },
    });

    expect(user2Webhooks).toHaveLength(1);
    expect(user2Webhooks[0].name).toBe('User 2 Webhook');
    expect(user2Domains).toHaveLength(1);
    expect(user2Domains[0].domain).toBe('user2.example.com');

    // Cross-user queries should return empty results
    const user1SeesUser2Webhooks = await prisma.webhook.findMany({
      where: { 
        AND: [
          { userId: user2.id }, 
          { id: user1Webhook.id }
        ]
      },
    });
    const user2SeesUser1Domains = await prisma.domain.findMany({
      where: { 
        AND: [
          { userId: user1.id }, 
          { id: user2Domain.id }
        ]
      },
    });

    expect(user1SeesUser2Webhooks).toHaveLength(0);
    expect(user2SeesUser1Domains).toHaveLength(0);
  });

  test('should maintain data isolation during updates', async () => {
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    const user1Webhook = await createTestWebhook(user1.id, { name: 'Original Name' });
    const user2Webhook = await createTestWebhook(user2.id, { name: 'Original Name' });

    // User 1 updates their webhook
    await prisma.webhook.update({
      where: { id: user1Webhook.id },
      data: { name: 'Updated by User 1' },
    });

    // User 2's webhook should remain unchanged
    const user2WebhookAfterUpdate = await prisma.webhook.findUnique({
      where: { id: user2Webhook.id },
    });

    expect(user2WebhookAfterUpdate?.name).toBe('Original Name');
  });

  test('should handle concurrent user operations safely', async () => {
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // Simulate concurrent webhook creation
    const webhookPromises = [
      createTestWebhook(user1.id, { name: 'User 1 Webhook A' }),
      createTestWebhook(user1.id, { name: 'User 1 Webhook B' }),
      createTestWebhook(user2.id, { name: 'User 2 Webhook A' }),
      createTestWebhook(user2.id, { name: 'User 2 Webhook B' }),
    ];

    const webhooks = await Promise.all(webhookPromises);

    // Verify all webhooks were created with correct ownership
    const user1Webhooks = webhooks.filter(w => w.userId === user1.id);
    const user2Webhooks = webhooks.filter(w => w.userId === user2.id);

    expect(user1Webhooks).toHaveLength(2);
    expect(user2Webhooks).toHaveLength(2);
  });

  test('should cascade delete user data properly', async () => {
    const user = await createTestUser({ email: '<EMAIL>' });
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomainWithCatchAll(user.id, webhook.id);

    // Verify data exists
    expect(await prisma.user.findUnique({ where: { id: user.id } })).toBeTruthy();
    // expect(await prisma.webhook.findUnique({ where: { id: webhook.id } })).toBeTruthy(); // TODO: Update for new architecture
    expect(await prisma.domain.findUnique({ where: { id: domain.id } })).toBeTruthy();

    // Delete user
    await prisma.user.delete({ where: { id: user.id } });

    // Verify cascade deletion
    expect(await prisma.user.findUnique({ where: { id: user.id } })).toBeNull();
    // expect(await prisma.webhook.findUnique({ where: { id: webhook.id } })).toBeNull(); // TODO: Update for new architecture
    expect(await prisma.domain.findUnique({ where: { id: domain.id } })).toBeNull();
  });

  test('should prevent unauthorized domain access via direct queries', async () => {
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    const webhook = await createTestWebhook(user1.id);
    const domain = await createTestDomainWithCatchAll(user1.id, webhook.id, {
      domain: 'private.example.com',
    });

    // Direct access to domain by ID should respect user ownership
    const unauthorizedAccess = await prisma.domain.findMany({
      where: {
        id: domain.domain.id,
        userId: user2.id, // Wrong user
      },
    });

    expect(unauthorizedAccess).toHaveLength(0);

    // Authorized access should work
    const authorizedAccess = await prisma.domain.findMany({
      where: {
        id: domain.domain.id,
        userId: user1.id, // Correct user
      },
    });

    expect(authorizedAccess).toHaveLength(1);
    expect(authorizedAccess[0].domain).toBe('private.example.com');
  });
});
