import { describe, test, expect } from '@jest/globals';
import {
  prisma,
  setupTestDatabase,
  createTestUser,
  createTestWebhook,
  createTestDomain,
  createTestDomainWithCatchAll
} from '../../setup/test-db-setup';

setupTestDatabase();

describe('Domain CRUD Operations', () => {
  test('should create user-domain-catchall relationships correctly', async () => {
    // Step 1: Create a test user
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Test User',
    });

    expect(testUser.email).toBe('<EMAIL>');
    expect(testUser.id).toBeDefined();

    // Step 2: Create a webhook for the user
    const testWebhook = await createTestWebhook(testUser.id, {
      name: 'Test Production Webhook',
      url: 'https://api.example.com/webhook',
      description: 'Primary webhook for production emails',
    });

    expect(testWebhook.name).toBe('Test Production Webhook');
    expect(testWebhook.userId).toBe(testUser.id);

    // Step 3: Create a domain with catch-all alias (new architecture)
    const { domain: testDomain, catchAllAlias } = await createTestDomainWithCatchAll(testUser.id, testWebhook.id, {
      domain: 'test-example.com',
      verified: false,
      verificationStatus: 'PENDING',
    });

    expect(testDomain.domain).toBe('test-example.com');
    expect(testDomain.userId).toBe(testUser.id);
    expect(catchAllAlias.email).toBe('*@test-example.com');
    // // expect(catchAllAlias.webhookId).toBe(testWebhook.id); // TODO: Update for new architecture // TODO: Update for new architecture
  });

  test('should query user-specific domains with relationships', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);
    const testDomain = await createTestDomainWithCatchAll(testUser.id, testWebhook.id);

    // Test querying user's domains with relationships
    const userDomains = await prisma.domain.findMany({
      where: { userId: testUser.id },
      include: { user: true },
    });
    
    expect(userDomains).toHaveLength(1);
    expect(userDomains[0].user.email).toBe(testUser.email);
    // expect(userDomains[0].webhook.name).toBe(testWebhook.name); // TODO: Update for new architecture
  });

  test('should enforce user isolation - users cannot see other users domains', async () => {
    // Create first user with domain
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const webhook1 = await createTestWebhook(user1.id);
    await createTestDomainWithCatchAll(user1.id, webhook1.id, { domain: 'user1.example.com' });

    // Create second user
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // User 1 should see their domain
    const user1Domains = await prisma.domain.findMany({
      where: { userId: user1.id },
    });
    expect(user1Domains).toHaveLength(1);
    expect(user1Domains[0].domain).toBe('user1.example.com');

    // User 2 should see no domains
    const user2Domains = await prisma.domain.findMany({
      where: { userId: user2.id },
    });
    expect(user2Domains).toHaveLength(0);
  });

  test('should support webhook switching for catch-all aliases', async () => {
    const testUser = await createTestUser();

    // Create two webhooks
    const webhook1 = await createTestWebhook(testUser.id, {
      name: 'Production Webhook',
      url: 'https://prod.example.com/webhook',
    });

    const webhook2 = await createTestWebhook(testUser.id, {
      name: 'Staging Webhook',
      url: 'https://staging.example.com/webhook',
    });

    // Create domain with catch-all alias using first webhook
    const { domain: testDomain, catchAllAlias } = await createTestDomainWithCatchAll(testUser.id, webhook1.id);

    // Verify initial webhook via catch-all alias
    let alias = await prisma.alias.findUnique({
      where: { id: catchAllAlias.id },
      ,
    });
    // expect(alias.webhook.name).toBe('Production Webhook'); // TODO: Update for new architecture

    // Switch catch-all alias to second webhook (new architecture)
    await prisma.alias.update({
      where: { id: catchAllAlias.id },
      data: { webhookId: webhook2.id },
    });

    // Verify webhook was switched
    alias = await prisma.alias.findUnique({
      where: { id: catchAllAlias.id },
      ,
    });
    // expect(alias.webhook.name).toBe('Staging Webhook'); // TODO: Update for new architecture
  });

  test('should handle cascade deletion properly', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);
    const { domain: testDomain, catchAllAlias } = await createTestDomainWithCatchAll(testUser.id, testWebhook.id);

    // Verify entities exist
    expect(await prisma.user.findUnique({ where: { id: testUser.id } })).toBeTruthy();
    // expect(await prisma.webhook.findUnique({ where: { id: testWebhook.id } })).toBeTruthy(); // TODO: Update for new architecture
    expect(await prisma.domain.findUnique({ where: { id: testDomain.id } })).toBeTruthy();
    expect(await prisma.alias.findUnique({ where: { id: catchAllAlias.id } })).toBeTruthy();

    // Delete user - should cascade delete webhook, domain, and aliases
    await prisma.user.delete({ where: { id: testUser.id } });

    // Verify cascaded deletion
    expect(await prisma.user.findUnique({ where: { id: testUser.id } })).toBeNull();
    // expect(await prisma.webhook.findUnique({ where: { id: testWebhook.id } })).toBeNull(); // TODO: Update for new architecture
    expect(await prisma.domain.findUnique({ where: { id: testDomain.id } })).toBeNull();
    expect(await prisma.alias.findUnique({ where: { id: catchAllAlias.id } })).toBeNull();
  });

  test('should validate required relationships', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);

    // Domain creation should fail without required userId
    await expect(
      prisma.domain.create({
        data: {
          domain: 'invalid.example.com',
          // Missing userId - this should fail at the database level
        } as any,
      })
    ).rejects.toThrow();

    // Alias creation should fail without required webhookId
    await expect(
      prisma.alias.create({
        data: {
          email: '<EMAIL>',
          domainId: 'invalid-domain-id',
          // Missing webhookId - this should fail at the database level
        } as any,
      })
    ).rejects.toThrow();
  });
});
