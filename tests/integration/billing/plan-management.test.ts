import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
} from '../../setup/test-db-setup';
import { PlanConfigService } from '../../../src/backend/services/billing/plan-config.service';
import { UserAuthService } from '../../../src/backend/services/auth/user-auth.service';

describe('Plan Management', () => {
  setupTestDatabase();

  const userAuthService = new UserAuthService();

  test('should get plan configuration for all plan types', () => {
    const freePlan = PlanConfigService.getPlanConfig('free');
    expect(freePlan.name).toBe('Free');
    expect(freePlan.monthlyEmailLimit).toBe(50);
    expect(freePlan.features).toContain('Up to 50 emails per month');

    const proPlan = PlanConfigService.getPlanConfig('pro');
    expect(proPlan.name).toBe('Pro');
    expect(proPlan.monthlyEmailLimit).toBe(1000);
    expect(proPlan.price).toBeDefined();

    const enterprisePlan = PlanConfigService.getPlanConfig('enterprise');
    expect(enterprisePlan.name).toBe('Enterprise');
    expect(enterprisePlan.monthlyEmailLimit).toBe(10000);
  });

  test('should throw error for unknown plan type', () => {
    expect(() => {
      PlanConfigService.getPlanConfig('unknown');
    }).toThrow('Unknown plan type: unknown');
  });

  test('should get plan limits correctly', () => {
    const freeLimits = PlanConfigService.getPlanLimits('free');
    expect(freeLimits.monthlyEmails).toBe(50);
    expect(freeLimits.domains).toBe(3); // Current free plan allows 3 domains
    expect(freeLimits.webhooks).toBe(3); // Current free plan allows 3 webhooks
    expect(freeLimits.aliases).toBe(10); // Current free plan allows 10 aliases

    const proLimits = PlanConfigService.getPlanLimits('pro');
    expect(proLimits.monthlyEmails).toBe(1000);
    expect(proLimits.domains).toBe(10); // Current pro plan allows 10 domains
    expect(proLimits.webhooks).toBe(10); // Current pro plan allows 10 webhooks per domain
    expect(proLimits.aliases).toBe(10); // Current pro plan allows 10 aliases per domain
  });

  test('should validate plan upgrades and downgrades', () => {
    expect(PlanConfigService.isValidUpgrade('free', 'pro')).toBe(true);
    expect(PlanConfigService.isValidUpgrade('pro', 'enterprise')).toBe(true);
    expect(PlanConfigService.isValidUpgrade('free', 'enterprise')).toBe(true);

    expect(PlanConfigService.isValidUpgrade('pro', 'free')).toBe(false);
    expect(PlanConfigService.isValidUpgrade('enterprise', 'pro')).toBe(false);

    expect(PlanConfigService.isValidDowngrade('pro', 'free')).toBe(true);
    expect(PlanConfigService.isValidDowngrade('enterprise', 'pro')).toBe(true);
    expect(PlanConfigService.isValidDowngrade('free', 'pro')).toBe(false);
  });

  test('should validate usage against plan limits', () => {
    // Valid usage for free plan (3 domains, 3 webhooks, 10 aliases)
    const validUsage = { domains: 2, webhooks: 2, aliases: 8 };
    const validation = PlanConfigService.validateUsageForPlan('free', validUsage);
    expect(validation.valid).toBe(true);
    expect(validation.violations).toHaveLength(0);

    // Invalid usage for free plan (exceeds limits)
    const invalidUsage = { domains: 5, webhooks: 5, aliases: 15 };
    const invalidValidation = PlanConfigService.validateUsageForPlan('free', invalidUsage);
    expect(invalidValidation.valid).toBe(false);
    expect(invalidValidation.violations.length).toBeGreaterThan(0);
  });

  test('should update user plan successfully', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
      monthlyEmailLimit: 50,
    });

    const result = await userAuthService.updateUserPlan(testUser.id, 'pro');
    expect(result.success).toBe(true);
    expect(result.user?.planType).toBe('pro');
    expect(result.user?.monthlyEmailLimit).toBe(1000);

    // Verify in database
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { planType: true, monthlyEmailLimit: true }
    });
    expect(updatedUser?.planType).toBe('pro');
    expect(updatedUser?.monthlyEmailLimit).toBe(1000);
  });

  test('should prevent plan change when usage exceeds limits', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: 1000,
    });

    // Create more domains than free plan allows
    const webhook = await prisma.webhook.create({
      data: {
        name: 'Test Webhook',
        url: 'https://example.com/webhook',
        userId: testUser.id
      }
    });

    // Create 4 domains (free plan only allows 3)
    for (let i = 0; i < 4; i++) {
      await prisma.domain.create({
        data: {
          domain: `test-domain-${i}.com`,
          userId: testUser.id
        }
      });
    }

    const result = await userAuthService.updateUserPlan(testUser.id, 'free');
    expect(result.success).toBe(false);
    expect(result.error).toContain('Too many domains');
  });

  test('should get user plan info correctly', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: 1000,
      currentMonthEmails: 150,
    });

    const webhook = await prisma.webhook.create({
      data: {
        name: 'Test Webhook',
        url: 'https://example.com/webhook',
        userId: testUser.id
      }
    });

    await prisma.domain.create({
      data: {
        domain: 'test-info.com',
        userId: testUser.id
      }
    });

    const planInfo = await userAuthService.getUserPlanInfo(testUser.id);
    expect(planInfo).toBeDefined();
    expect(planInfo?.planConfig.name).toBe('Pro');
    expect(planInfo?.usage.emails).toBe(150);
    expect(planInfo?.usage.domains).toBe(1);
    // expect(planInfo?.usage.webhooks).toBe(1); // TODO: Update for new architecture
    expect(planInfo?.limits.emails).toBe(1000);
    expect(planInfo?.limits.domains).toBe(10); // Pro plan allows 10 domains
  });

  test('should handle non-existent user gracefully', async () => {
    const result = await userAuthService.updateUserPlan('non-existent-id', 'pro');
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not found');

    const planInfo = await userAuthService.getUserPlanInfo('non-existent-id');
    expect(planInfo).toBeNull();
  });

  test('should handle invalid plan type in update', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
    });

    const result = await userAuthService.updateUserPlan(testUser.id, 'invalid-plan');
    expect(result.success).toBe(false);
    expect(result.error).toContain('Unknown plan type');
  });
});
